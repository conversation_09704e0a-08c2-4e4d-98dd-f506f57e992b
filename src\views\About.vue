<template>
  <div class="about">
    <header class="hero">
      <h1>📖 关于我们</h1>
      <p>了解更多关于这个Vue3学习项目</p>
    </header>

    <main class="content">
      <div class="card">
        <h2>项目介绍</h2>
        <p>这是一个专门为学习Vue3而创建的示例项目。通过这个项目，你可以学习到Vue3的核心概念和最佳实践。</p>
        
        <div class="features">
          <div class="feature-item" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">{{ feature.icon }}</div>
            <div class="feature-content">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <h2>技术栈</h2>
        <div class="tech-stack">
          <div class="tech-item" v-for="tech in techStack" :key="tech.name">
            <div class="tech-icon">{{ tech.icon }}</div>
            <div class="tech-info">
              <h4>{{ tech.name }}</h4>
              <p>{{ tech.version }}</p>
              <span class="tech-description">{{ tech.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <h2>学习进度</h2>
        <div class="progress-section">
          <div class="progress-item" v-for="item in learningProgress" :key="item.id">
            <div class="progress-header">
              <span class="progress-title">{{ item.title }}</span>
              <span class="progress-percentage">{{ item.progress }}%</span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: item.progress + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <h2>联系信息</h2>
        <div class="contact-info">
          <p>📧 邮箱: <EMAIL></p>
          <p>🌐 网站: https://vue-learn.example.com</p>
          <p>📱 GitHub: https://github.com/vue-learn</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const features = ref([
  {
    id: 1,
    icon: '🎯',
    title: '组合式API',
    description: '学习Vue3最新的组合式API，更好地组织和复用代码逻辑'
  },
  {
    id: 2,
    icon: '⚡',
    title: '响应式系统',
    description: '深入理解Vue3的响应式原理，掌握ref、reactive等核心概念'
  },
  {
    id: 3,
    icon: '🧩',
    title: '组件化开发',
    description: '学习如何构建可复用的组件，提高开发效率'
  },
  {
    id: 4,
    icon: '🛣️',
    title: '路由管理',
    description: '使用Vue Router实现单页应用的路由功能'
  }
])

const techStack = ref([
  {
    name: 'Vue 3',
    version: 'v3.3.4',
    icon: '💚',
    description: '渐进式JavaScript框架'
  },
  {
    name: 'Vue Router',
    version: 'v4.2.4',
    icon: '🛣️',
    description: '官方路由管理器'
  },
  {
    name: 'Vite',
    version: 'v4.4.5',
    icon: '⚡',
    description: '下一代前端构建工具'
  },
  {
    name: 'JavaScript',
    version: 'ES6+',
    icon: '📜',
    description: '现代JavaScript语法'
  }
])

const learningProgress = ref([
  { id: 1, title: 'Vue3基础语法', progress: 85 },
  { id: 2, title: '组合式API', progress: 70 },
  { id: 3, title: '组件通信', progress: 45 },
  { id: 4, title: '路由管理', progress: 60 },
  { id: 5, title: '状态管理', progress: 20 }
])
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.hero {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.content {
  display: grid;
  gap: 20px;
}

.card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.features {
  display: grid;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.feature-item:hover {
  background: #e9ecef;
}

.feature-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.feature-content h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.2rem;
}

.feature-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.tech-stack {
  display: grid;
  gap: 15px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.tech-icon {
  font-size: 1.5rem;
}

.tech-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.tech-info p {
  margin: 0 0 5px 0;
  color: #667eea;
  font-weight: bold;
  font-size: 0.9rem;
}

.tech-description {
  color: #666;
  font-size: 0.9rem;
}

.progress-section {
  display: grid;
  gap: 20px;
}

.progress-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-title {
  font-weight: bold;
  color: #333;
}

.progress-percentage {
  color: #667eea;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.contact-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.contact-info p {
  margin: 10px 0;
  color: #333;
  font-size: 1.1rem;
}

p {
  color: #666;
  line-height: 1.6;
}
</style>
