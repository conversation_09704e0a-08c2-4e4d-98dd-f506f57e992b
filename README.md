# Vue3 学习项目

这是一个简单的Vue3学习项目，包含了Vue3的基础功能演示。

## 功能特性

- ✨ Vue3 组合式API (Composition API)
- 🎯 响应式数据绑定
- 🔄 事件处理
- 📝 表单输入
- 📋 列表渲染
- 🛣️ Vue Router 路由管理
- 📱 多页面导航
- 🎨 现代化UI设计
- 📊 学习进度展示

## 项目结构

```
vue-learn/
├── index.html          # 入口HTML文件
├── package.json        # 项目配置和依赖
├── vite.config.js      # Vite构建配置
├── src/
│   ├── main.js         # 应用入口文件
│   ├── App.vue         # 主组件(包含导航)
│   ├── router/
│   │   └── index.js    # 路由配置
│   └── views/
│       ├── Home.vue    # 首页组件
│       └── About.vue   # 关于页面组件
└── README.md           # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 页面导航

- **首页 (/)**: 包含计数器、输入框和待办事项功能
- **关于页面 (/about)**: 项目介绍、技术栈和学习进度

### 3. 构建生产版本

```bash
npm run build
```

## 学习内容

这个项目演示了以下Vue3核心概念：

1. **组合式API**: 使用 `<script setup>` 语法
2. **响应式数据**: 使用 `ref()` 创建响应式变量
3. **事件处理**: 按钮点击事件和键盘事件
4. **双向数据绑定**: `v-model` 指令
5. **条件渲染**: `v-if` 和 `v-else` 指令
6. **列表渲染**: `v-for` 指令
7. **路由管理**: Vue Router 实现页面导航
8. **组件化开发**: 多个页面组件
9. **样式绑定**: Scoped CSS 和动态样式

## 技术栈

- Vue 3.3+
- Vue Router 4.2+
- Vite 4.4+
- 现代ES6+语法

## 下一步学习

- 组件通信 (props, emit)
- 生命周期钩子
- 计算属性和侦听器
- 路由 (Vue Router)
- 状态管理 (Pinia)
