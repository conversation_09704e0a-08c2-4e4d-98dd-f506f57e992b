# Vue3 项目添加新路由指南

本文档详细说明如何在当前Vue3项目中添加新的路由页面。

## 📋 目录
- [前置知识](#前置知识)
- [添加路由的完整步骤](#添加路由的完整步骤)
- [实际示例：添加联系页面](#实际示例添加联系页面)
- [路由参数和动态路由](#路由参数和动态路由)
- [路由守卫](#路由守卫)
- [常见问题](#常见问题)

## 前置知识

在开始之前，确保你了解以下概念：
- Vue3 组合式API
- Vue Router 4 基础概念
- 单文件组件(.vue文件)结构

## 添加路由的完整步骤

### 步骤1: 创建新的页面组件

在 `src/views/` 目录下创建新的Vue组件文件。

**文件位置**: `src/views/YourPageName.vue`

```vue
<template>
  <div class="your-page">
    <header class="hero">
      <h1>🎯 页面标题</h1>
      <p>页面描述</p>
    </header>
    
    <main class="content">
      <div class="card">
        <h2>页面内容</h2>
        <p>在这里添加你的页面内容</p>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 在这里添加你的响应式数据和方法
const message = ref('Hello from new page!')
</script>

<style scoped>
/* 复用现有样式或添加新样式 */
.your-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.hero {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content {
  display: grid;
  gap: 20px;
}

.card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}
</style>
```

### 步骤2: 在路由配置中注册新路由

编辑 `src/router/index.js` 文件：

```javascript
import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import YourPageName from '../views/YourPageName.vue' // 1. 导入新组件

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    // 2. 添加新路由配置
    path: '/your-page-path',        // URL路径
    name: 'YourPageName',           // 路由名称
    component: YourPageName         // 对应的组件
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

### 步骤3: 在导航栏中添加链接

编辑 `src/App.vue` 文件，在导航菜单中添加新链接：

```vue
<ul class="nav-menu">
  <li class="nav-item">
    <router-link to="/" class="nav-link">🏠 首页</router-link>
  </li>
  <li class="nav-item">
    <router-link to="/about" class="nav-link">📖 关于</router-link>
  </li>
  <!-- 添加新的导航链接 -->
  <li class="nav-item">
    <router-link to="/your-page-path" class="nav-link">🎯 你的页面</router-link>
  </li>
</ul>
```

## 实际示例：添加联系页面

让我们通过一个具体例子来演示整个过程：

### 1. 创建联系页面组件

**文件**: `src/views/Contact.vue`

```vue
<template>
  <div class="contact">
    <header class="hero">
      <h1>📞 联系我们</h1>
      <p>有问题？我们很乐意为您提供帮助</p>
    </header>

    <main class="content">
      <div class="card">
        <h2>发送消息</h2>
        <form @submit.prevent="submitForm" class="contact-form">
          <div class="form-group">
            <label for="name">姓名</label>
            <input 
              id="name"
              v-model="form.name" 
              type="text" 
              required 
              class="input"
            />
          </div>
          
          <div class="form-group">
            <label for="email">邮箱</label>
            <input 
              id="email"
              v-model="form.email" 
              type="email" 
              required 
              class="input"
            />
          </div>
          
          <div class="form-group">
            <label for="message">消息</label>
            <textarea 
              id="message"
              v-model="form.message" 
              required 
              class="textarea"
              rows="5"
            ></textarea>
          </div>
          
          <button type="submit" class="btn-submit">发送消息</button>
        </form>
        
        <div v-if="submitted" class="success-message">
          ✅ 消息发送成功！我们会尽快回复您。
        </div>
      </div>

      <div class="card">
        <h2>联系信息</h2>
        <div class="contact-info">
          <p>📧 邮箱: <EMAIL></p>
          <p>📱 电话: +86 123-4567-8900</p>
          <p>🏢 地址: 北京市朝阳区某某街道123号</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const form = ref({
  name: '',
  email: '',
  message: ''
})

const submitted = ref(false)

const submitForm = () => {
  // 这里可以添加实际的表单提交逻辑
  console.log('表单数据:', form.value)
  submitted.value = true
  
  // 3秒后重置表单
  setTimeout(() => {
    form.value = { name: '', email: '', message: '' }
    submitted.value = false
  }, 3000)
}
</script>

<style scoped>
.contact {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.hero {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content {
  display: grid;
  gap: 20px;
}

.card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form {
  display: grid;
  gap: 20px;
}

.form-group {
  display: grid;
  gap: 8px;
}

.form-group label {
  font-weight: bold;
  color: #333;
}

.input, .textarea {
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #667eea;
}

.btn-submit {
  background: #667eea;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-submit:hover {
  background: #5a6fd8;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #c3e6cb;
}

.contact-info p {
  margin: 15px 0;
  font-size: 1.1rem;
  color: #333;
}
</style>
```

### 2. 更新路由配置

在 `src/router/index.js` 中添加：

```javascript
import Contact from '../views/Contact.vue'

const routes = [
  // ... 现有路由
  {
    path: '/contact',
    name: 'Contact',
    component: Contact
  }
]
```

### 3. 更新导航栏

在 `src/App.vue` 中添加：

```vue
<li class="nav-item">
  <router-link to="/contact" class="nav-link">📞 联系</router-link>
</li>
```

## 路由参数和动态路由

### 动态路由示例

```javascript
// 路由配置
{
  path: '/user/:id',
  name: 'UserProfile',
  component: UserProfile
}

// 在组件中获取参数
<script setup>
import { useRoute } from 'vue-router'

const route = useRoute()
const userId = route.params.id
</script>
```

### 查询参数

```javascript
// URL: /search?keyword=vue&category=tutorial
const route = useRoute()
const keyword = route.query.keyword
const category = route.query.category
```

## 路由守卫

### 全局前置守卫

```javascript
// src/router/index.js
router.beforeEach((to, from, next) => {
  // 路由跳转前的逻辑
  console.log('导航到:', to.path)
  next()
})
```

### 组件内守卫

```vue
<script setup>
import { onBeforeRouteEnter, onBeforeRouteLeave } from 'vue-router'

onBeforeRouteEnter((to, from, next) => {
  // 进入路由前
  next()
})

onBeforeRouteLeave((to, from, next) => {
  // 离开路由前
  next()
})
</script>
```

## 常见问题

### Q: 路由不生效怎么办？
A: 检查以下几点：
1. 组件是否正确导入
2. 路由路径是否正确
3. 组件名称是否匹配
4. 是否重启了开发服务器

### Q: 如何实现路由懒加载？
A: 使用动态导入：
```javascript
{
  path: '/about',
  name: 'About',
  component: () => import('../views/About.vue')
}
```

### Q: 如何设置默认路由？
A: 添加重定向：
```javascript
{
  path: '/',
  redirect: '/home'
}
```

### Q: 如何处理404页面？
A: 添加通配符路由：
```javascript
{
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: NotFound
}
```

## 总结

添加新路由的核心步骤：
1. 📄 创建页面组件
2. 🛣️ 配置路由
3. 🧭 添加导航链接

遵循这个流程，你就可以轻松地为Vue3项目添加新的页面和功能了！
