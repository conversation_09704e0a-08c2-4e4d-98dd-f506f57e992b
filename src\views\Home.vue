<template>
  <div class="home">
    <header class="hero">
      <h1>🏠 首页</h1>
      <p>欢迎来到Vue3学习项目的首页</p>
    </header>

    <main class="content">
      <div class="card">
        <h2>计数器示例</h2>
        <div class="counter">
          <button @click="decrement" class="btn">-</button>
          <span class="count">{{ count }}</span>
          <button @click="increment" class="btn">+</button>
        </div>
        <p>当前计数: {{ count }}</p>
      </div>

      <div class="card">
        <h2>输入框示例</h2>
        <input 
          v-model="message" 
          placeholder="请输入一些文字..."
          class="input"
        />
        <p v-if="message">你输入的内容: {{ message }}</p>
        <p v-else class="placeholder">请在上方输入框中输入内容</p>
      </div>

      <div class="card">
        <h2>待办事项</h2>
        <div class="todo-input">
          <input 
            v-model="newTodo" 
            @keyup.enter="addTodo"
            placeholder="添加新的待办事项..."
            class="input"
          />
          <button @click="addTodo" class="btn-add">添加</button>
        </div>
        <ul class="todo-list" v-if="todos.length">
          <li v-for="todo in todos" :key="todo.id" class="todo-item">
            <span :class="{ completed: todo.completed }" @click="toggleTodo(todo.id)">
              {{ todo.text }}
            </span>
            <button @click="removeTodo(todo.id)" class="btn-remove">删除</button>
          </li>
        </ul>
        <p v-else class="placeholder">暂无待办事项</p>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 计数器
const count = ref(0)
const increment = () => count.value++
const decrement = () => count.value--

// 输入框
const message = ref('')

// 待办事项
const newTodo = ref('')
const todos = ref([
  { id: 1, text: '学习Vue3基础', completed: false },
  { id: 2, text: '掌握Vue Router', completed: false }
])

const addTodo = () => {
  if (newTodo.value.trim()) {
    todos.value.push({
      id: Date.now(),
      text: newTodo.value.trim(),
      completed: false
    })
    newTodo.value = ''
  }
}

const toggleTodo = (id) => {
  const todo = todos.value.find(t => t.id === id)
  if (todo) {
    todo.completed = !todo.completed
  }
}

const removeTodo = (id) => {
  todos.value = todos.value.filter(t => t.id !== id)
}
</script>

<style scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.hero {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.content {
  display: grid;
  gap: 20px;
}

.card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.btn {
  background: #667eea;
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

.count {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  min-width: 60px;
  text-align: center;
}

.input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #667eea;
}

.placeholder {
  color: #999;
  font-style: italic;
}

.todo-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.btn-add {
  background: #28a745;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.btn-add:hover {
  background: #218838;
}

.todo-list {
  list-style: none;
  padding: 0;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.todo-item span {
  cursor: pointer;
  flex: 1;
  transition: all 0.3s ease;
}

.todo-item span.completed {
  text-decoration: line-through;
  color: #999;
}

.btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.btn-remove:hover {
  background: #c82333;
}

p {
  color: #666;
  line-height: 1.6;
}
</style>
